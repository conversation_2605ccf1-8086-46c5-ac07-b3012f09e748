//
//  AppSwitcherViewController.swift
//  Switchman
//
//  Created by Augment Agent on 7/24/25.
//  Copyright © 2025 AlvinZhu. All rights reserved.
//

import Cocoa
import KeyboardShortcuts

class AppSwitcherViewController: NSViewController {
    
    // MARK: - Properties
    
    private var collectionView: NSCollectionView!
    private var scrollView: NSScrollView!
    private var backgroundView: NSVisualEffectView!
    
    private var apps: [AppModel] = []
    private var selectedIndex: Int = 0
    
    // Callbacks
    var onClose: (() -> Void)?
    var onAppSelected: ((AppModel) -> Void)?
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 600, height: 400))
        setupUI()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        refreshApps()
    }
    
    override func viewDidAppear() {
        super.viewDidAppear()
        view.window?.makeFirstResponder(self)
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Background with blur effect
        backgroundView = NSVisualEffectView(frame: view.bounds)
        backgroundView.material = .hudWindow
        backgroundView.blendingMode = .behindWindow
        backgroundView.state = .active
        backgroundView.wantsLayer = true
        backgroundView.layer?.cornerRadius = 12
        backgroundView.autoresizingMask = [.width, .height]
        view.addSubview(backgroundView)
        
        // Collection view setup
        let flowLayout = NSCollectionViewFlowLayout()
        flowLayout.itemSize = NSSize(width: 120, height: 110) // Increased height for better layout
        flowLayout.minimumInteritemSpacing = 20
        flowLayout.minimumLineSpacing = 20
        flowLayout.sectionInset = NSEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
        
        collectionView = NSCollectionView()
        collectionView.collectionViewLayout = flowLayout
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.isSelectable = true
        collectionView.allowsMultipleSelection = false
        collectionView.backgroundColors = [NSColor.clear]
        
        // Register the cell
        collectionView.register(AppSwitcherCollectionViewItem.self, 
                               forItemWithIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"))
        
        // Scroll view
        scrollView = NSScrollView(frame: view.bounds.insetBy(dx: 20, dy: 20))
        scrollView.documentView = collectionView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.backgroundColor = NSColor.clear
        scrollView.autoresizingMask = [.width, .height]
        
        view.addSubview(scrollView)
        
        // Add title label
        let titleLabel = NSTextField(labelWithString: "App Switcher")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.frame = NSRect(x: 0, y: view.bounds.height - 50, width: view.bounds.width, height: 30)
        titleLabel.autoresizingMask = [.width, .minYMargin]
        view.addSubview(titleLabel)

        // Add settings button
        let settingsButton = NSButton(frame: NSRect(x: view.bounds.width - 50, y: view.bounds.height - 50, width: 30, height: 30))
        settingsButton.title = ""
        settingsButton.bezelStyle = .circular
        settingsButton.image = NSImage(systemSymbolName: "gearshape", accessibilityDescription: "Settings")
        settingsButton.target = self
        settingsButton.action = #selector(openSettings)
        settingsButton.autoresizingMask = [.minXMargin, .minYMargin]
        view.addSubview(settingsButton)

        // Adjust scroll view frame to account for title
        scrollView.frame = NSRect(x: 20, y: 20, width: view.bounds.width - 40, height: view.bounds.height - 80)
    }
    
    // MARK: - Data Management
    
    func refreshApps() {
        apps = AppsManager.manager.selectedApps
        collectionView?.reloadData()
        
        // Select first item if available
        if !apps.isEmpty {
            selectedIndex = 0
            selectItem(at: selectedIndex)
        }
    }
    
    private func selectItem(at index: Int) {
        guard index >= 0 && index < apps.count else { return }

        selectedIndex = index
        let indexPath = IndexPath(item: index, section: 0)

        // Clear previous selection
        collectionView.deselectAll(nil)

        // Select the new item
        collectionView.selectItems(at: Set([indexPath]), scrollPosition: .nearestHorizontalEdge)
    }
    
    // MARK: - Keyboard Handling
    
    override func keyDown(with event: NSEvent) {
        let keyCode = event.keyCode
        
        switch keyCode {
        case 36, 76: // Return or Enter
            if selectedIndex < apps.count {
                onAppSelected?(apps[selectedIndex])
            }
            
        case 53: // Escape
            onClose?()
            
        case 125: // Down arrow
            if selectedIndex < apps.count - 1 {
                selectItem(at: selectedIndex + 1)
            }
            
        case 126: // Up arrow
            if selectedIndex > 0 {
                selectItem(at: selectedIndex - 1)
            }
            
        case 123: // Left arrow
            let itemsPerRow = Int(scrollView.bounds.width / 140) // Approximate items per row
            let newIndex = max(0, selectedIndex - itemsPerRow)
            selectItem(at: newIndex)
            
        case 124: // Right arrow
            let itemsPerRow = Int(scrollView.bounds.width / 140) // Approximate items per row
            let newIndex = min(apps.count - 1, selectedIndex + itemsPerRow)
            selectItem(at: newIndex)
            
        default:
            super.keyDown(with: event)
        }
    }
    
    override var acceptsFirstResponder: Bool {
        return true
    }

    // MARK: - Actions

    @objc private func openSettings() {
        // Close the App Switcher
        onClose?()

        // Open the main settings window
        sharedAppDelegate?.showMainWindow()
    }

}

// MARK: - NSCollectionViewDataSource

extension AppSwitcherViewController: NSCollectionViewDataSource {
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return apps.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"), 
                                          for: indexPath) as! AppSwitcherCollectionViewItem
        
        let app = apps[indexPath.item]
        item.configure(with: app)
        
        return item
    }
}

// MARK: - NSCollectionViewDelegate

extension AppSwitcherViewController: NSCollectionViewDelegate {
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        if let indexPath = indexPaths.first {
            selectedIndex = indexPath.item
            onAppSelected?(apps[selectedIndex])
        }
    }
}

// MARK: - AppSwitcherCollectionViewItem

class AppSwitcherCollectionViewItem: NSCollectionViewItem {

    private var iconImageView: NSImageView!
    private var nameLabel: NSTextField!
    private var shortcutLabel: NSTextField!
    private var containerView: NSView!

    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 120, height: 110))
        setupUI()
    }

    private func setupUI() {
        // Container view with background
        containerView = NSView()
        containerView.wantsLayer = true
        containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.3).cgColor
        containerView.layer?.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(containerView)

        // App name (moved to top)
        nameLabel = NSTextField(labelWithString: "")
        nameLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        nameLabel.textColor = NSColor.labelColor
        nameLabel.alignment = .center
        nameLabel.isEditable = false
        nameLabel.isBordered = false
        nameLabel.backgroundColor = NSColor.clear
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)

        // Icon (moved to center)
        iconImageView = NSImageView()
        iconImageView.imageScaling = .scaleProportionallyUpOrDown
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconImageView)

        // Shortcut (at bottom)
        shortcutLabel = NSTextField(labelWithString: "")
        shortcutLabel.font = NSFont.systemFont(ofSize: 10)
        shortcutLabel.textColor = NSColor.secondaryLabelColor
        shortcutLabel.alignment = .center
        shortcutLabel.isEditable = false
        shortcutLabel.isBordered = false
        shortcutLabel.backgroundColor = NSColor.clear
        shortcutLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(shortcutLabel)

        // Auto Layout constraints
        NSLayoutConstraint.activate([
            // Container view fills the entire cell
            containerView.topAnchor.constraint(equalTo: view.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // App name at the top
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            nameLabel.heightAnchor.constraint(equalToConstant: 16),

            // Icon in the center
            iconImageView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconImageView.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
            iconImageView.widthAnchor.constraint(equalToConstant: 48),
            iconImageView.heightAnchor.constraint(equalToConstant: 48),

            // Shortcut at the bottom
            shortcutLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 8),
            shortcutLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            shortcutLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            shortcutLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -8),
            shortcutLabel.heightAnchor.constraint(equalToConstant: 14)
        ])
    }

    func configure(with app: AppModel) {
        iconImageView.image = app.icon
        nameLabel.stringValue = app.appDisplayName

        // Format shortcut display
        if let shortcut = app.shortcut {
            shortcutLabel.stringValue = shortcut.description
        } else {
            shortcutLabel.stringValue = "No shortcut"
        }
    }

    override var isSelected: Bool {
        didSet {
            updateSelection()
        }
    }

    private func updateSelection() {
        if isSelected {
            containerView.layer?.backgroundColor = NSColor.selectedControlColor.withAlphaComponent(0.6).cgColor
            containerView.layer?.borderWidth = 2
            containerView.layer?.borderColor = NSColor.selectedControlColor.cgColor
        } else {
            containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.3).cgColor
            containerView.layer?.borderWidth = 0
        }
    }
}
