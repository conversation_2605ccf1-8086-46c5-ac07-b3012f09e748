//
//  AppSwitcherViewController.swift
//  Switchman
//
//  Created by Augment Agent on 7/24/25.
//  Copyright © 2025 AlvinZhu. All rights reserved.
//

import Cocoa
import KeyboardShortcuts

class AppSwitcherViewController: NSViewController {
    
    // MARK: - Properties

    private var collectionView: NSCollectionView!
    private var scrollView: NSScrollView!
    private var backgroundView: NSVisualEffectView!

    private var apps: [AppModel] = []
    private var selectedIndex: Int = 0

    // Layout configuration
    private var maxItemsPerRow: Int = 8
    private var preferredItemSize: NSSize = NSSize(width: 100, height: 90)

    // Callbacks
    var onClose: (() -> Void)?
    var onAppSelected: ((AppModel) -> Void)?
    
    // MARK: - Lifecycle
    
    override func loadView() {
        // Dynamic sizing based on screen and number of apps
        let screenSize = NSScreen.main?.frame.size ?? NSSize(width: 1920, height: 1080)
        let maxWidth = min(screenSize.width * 0.8, 1200)
        let maxHeight = min(screenSize.height * 0.7, 800)

        view = NSView(frame: NSRect(x: 0, y: 0, width: maxWidth, height: maxHeight))
        setupUI()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        refreshApps()
    }
    
    override func viewDidAppear() {
        super.viewDidAppear()
        view.window?.makeFirstResponder(self)
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // Background with blur effect
        backgroundView = NSVisualEffectView(frame: view.bounds)
        backgroundView.material = .hudWindow
        backgroundView.blendingMode = .behindWindow
        backgroundView.state = .active
        backgroundView.wantsLayer = true
        backgroundView.layer?.cornerRadius = 12
        backgroundView.autoresizingMask = [.width, .height]
        view.addSubview(backgroundView)

        // Collection view setup with dynamic layout
        let flowLayout = createOptimalLayout()

        collectionView = NSCollectionView()
        collectionView.collectionViewLayout = flowLayout
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.isSelectable = true
        collectionView.allowsMultipleSelection = false
        collectionView.backgroundColors = [NSColor.clear]

        // Register the cell
        collectionView.register(AppSwitcherCollectionViewItem.self,
                               forItemWithIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"))

        // Scroll view
        scrollView = NSScrollView(frame: view.bounds.insetBy(dx: 20, dy: 20))
        scrollView.documentView = collectionView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.backgroundColor = NSColor.clear
        scrollView.autoresizingMask = [.width, .height]

        view.addSubview(scrollView)
        
        // Add title label
        let titleLabel = NSTextField(labelWithString: "App Switcher")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.frame = NSRect(x: 0, y: view.bounds.height - 50, width: view.bounds.width, height: 30)
        titleLabel.autoresizingMask = [.width, .minYMargin]
        view.addSubview(titleLabel)

        // Add settings button
        let settingsButton = NSButton(frame: NSRect(x: view.bounds.width - 50, y: view.bounds.height - 50, width: 30, height: 30))
        settingsButton.title = ""
        settingsButton.bezelStyle = .circular
        settingsButton.image = NSImage(systemSymbolName: "gearshape", accessibilityDescription: "Settings")
        settingsButton.target = self
        settingsButton.action = #selector(openSettings)
        settingsButton.autoresizingMask = [.minXMargin, .minYMargin]
        view.addSubview(settingsButton)

        // Adjust scroll view frame to account for title
        scrollView.frame = NSRect(x: 20, y: 20, width: view.bounds.width - 40, height: view.bounds.height - 80)
    }

    // MARK: - Layout Management

    private func createOptimalLayout() -> NSCollectionViewFlowLayout {
        let flowLayout = NSCollectionViewFlowLayout()

        // Calculate optimal item size and spacing based on available space and number of apps
        let availableWidth = view.bounds.width - 80 // Account for margins and scroll bars
        let availableHeight = view.bounds.height - 120 // Account for title and margins

        let appCount = apps.count
        let minSpacing: CGFloat = 12

        // Determine optimal layout based on app count
        var itemsPerRow: Int
        var itemWidth: CGFloat
        var itemHeight: CGFloat

        if appCount <= 4 {
            // For very few apps, use larger items in a single row
            itemsPerRow = appCount
            itemWidth = min(140, (availableWidth - CGFloat(itemsPerRow - 1) * minSpacing) / CGFloat(itemsPerRow))
            itemHeight = 110
        } else if appCount <= 8 {
            // For few apps, use larger items in a single row
            itemsPerRow = min(appCount, 8)
            itemWidth = min(120, (availableWidth - CGFloat(itemsPerRow - 1) * minSpacing) / CGFloat(itemsPerRow))
            itemHeight = 100
        } else if appCount <= 16 {
            // For medium number of apps, use 2 rows
            itemsPerRow = min(8, (appCount + 1) / 2)
            itemWidth = min(100, (availableWidth - CGFloat(itemsPerRow - 1) * minSpacing) / CGFloat(itemsPerRow))
            itemHeight = 90
        } else if appCount <= 32 {
            // For many apps, optimize for space efficiency
            itemsPerRow = min(maxItemsPerRow, max(6, Int(sqrt(Double(appCount)) * 1.3)))
            itemWidth = min(85, (availableWidth - CGFloat(itemsPerRow - 1) * minSpacing) / CGFloat(itemsPerRow))
            itemHeight = 75
        } else {
            // For very many apps, use compact layout
            itemsPerRow = min(maxItemsPerRow, max(8, Int(sqrt(Double(appCount)) * 1.5)))
            itemWidth = min(75, (availableWidth - CGFloat(itemsPerRow - 1) * minSpacing) / CGFloat(itemsPerRow))
            itemHeight = 65
        }

        // Ensure minimum size
        itemWidth = max(itemWidth, 70)
        itemHeight = max(itemHeight, 70)

        flowLayout.itemSize = NSSize(width: itemWidth, height: itemHeight)
        flowLayout.minimumInteritemSpacing = minSpacing
        flowLayout.minimumLineSpacing = minSpacing
        flowLayout.sectionInset = NSEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)

        return flowLayout
    }

    private func updateLayoutForAppCount() {
        let newLayout = createOptimalLayout()
        collectionView.collectionViewLayout = newLayout

        // Dynamically resize the window if needed
        let appCount = apps.count
        if appCount > 0 {
            let layout = newLayout
            // Use collectionView.bounds.width for a more accurate calculation
            let itemsPerRow = max(1, Int((collectionView.bounds.width - layout.sectionInset.left - layout.sectionInset.right) / (layout.itemSize.width + layout.minimumInteritemSpacing)))
            let numberOfRows = max(1, (appCount + itemsPerRow - 1) / itemsPerRow)

            let requiredWidth = min(CGFloat(itemsPerRow) * layout.itemSize.width +
                                  CGFloat(itemsPerRow - 1) * layout.minimumInteritemSpacing +
                                  layout.sectionInset.left + layout.sectionInset.right + 40, // margins
                                  NSScreen.main?.frame.width ?? 1920)

            let requiredHeight = CGFloat(numberOfRows) * layout.itemSize.height +
                               CGFloat(numberOfRows - 1) * layout.minimumLineSpacing +
                               layout.sectionInset.top + layout.sectionInset.bottom + 100 // Title and margins

            let maxHeight = NSScreen.main?.frame.height ?? 1080
            let targetWidth = max(400, requiredWidth)
            let targetHeight = min(requiredHeight, maxHeight * 0.7)

            // Only resize if there's a significant difference
            if abs(view.bounds.width - targetWidth) > 50 || abs(view.bounds.height - targetHeight) > 20 {
                view.setFrameSize(NSSize(width: targetWidth, height: targetHeight))
                view.window?.setContentSize(view.bounds.size)
                view.window?.center()

                // Update scroll view frame
                scrollView.frame = NSRect(x: 20, y: 20, width: targetWidth - 40, height: targetHeight - 80)
            }
        }
    }

    // MARK: - Data Management
    
    func refreshApps() {
        apps = AppsManager.manager.selectedApps

        // Update layout based on app count
        updateLayoutForAppCount()

        collectionView?.reloadData()

        // Select first item if available
        if !apps.isEmpty {
            selectedIndex = 0
            selectItem(at: selectedIndex)
        }
    }
    
    private func selectItem(at index: Int) {
        guard index >= 0 && index < apps.count else { return }

        selectedIndex = index
        let indexPath = IndexPath(item: index, section: 0)

        // Clear previous selection
        collectionView.deselectAll(nil)

        // Select the new item with smooth scrolling
        collectionView.selectItems(at: Set([indexPath]), scrollPosition: .centeredVertically)

        // Ensure the selected item is visible
        collectionView.scrollToItems(at: Set([indexPath]), scrollPosition: .centeredVertically)
    }
    
    // MARK: - Keyboard Handling
    
    override func keyDown(with event: NSEvent) {
        let keyCode = event.keyCode
        
        switch keyCode {
        case 36, 76: // Return or Enter
            if selectedIndex < apps.count {
                onAppSelected?(apps[selectedIndex])
            }
            
        case 53: // Escape
            onClose?()
            
        case 125: // Down arrow
            if selectedIndex < apps.count - 1 {
                selectItem(at: selectedIndex + 1)
            }
            
        case 126: // Up arrow
            if selectedIndex > 0 {
                selectItem(at: selectedIndex - 1)
            }
            
        case 123: // Left arrow
            let itemsPerRow = calculateItemsPerRow()
            let newIndex = max(0, selectedIndex - itemsPerRow)
            selectItem(at: newIndex)

        case 124: // Right arrow
            let itemsPerRow = calculateItemsPerRow()
            let newIndex = min(apps.count - 1, selectedIndex + itemsPerRow)
            selectItem(at: newIndex)
            
        default:
            super.keyDown(with: event)
        }
    }
    
    override var acceptsFirstResponder: Bool {
        return true
    }

    private func calculateItemsPerRow() -> Int {
        guard let layout = collectionView.collectionViewLayout as? NSCollectionViewFlowLayout else {
            return 1
        }

        // Use collectionView.bounds.width for a more accurate calculation of available width
        let availableWidth = collectionView.bounds.width - layout.sectionInset.left - layout.sectionInset.right
        let itemWidthWithSpacing = layout.itemSize.width + layout.minimumInteritemSpacing
        return max(1, Int(availableWidth / itemWidthWithSpacing))
    }

    // MARK: - Actions

    @objc private func openSettings() {
        // Close the App Switcher
        onClose?()

        // Open the main settings window
        sharedAppDelegate?.showMainWindow()
    }

}

// MARK: - NSCollectionViewDataSource

extension AppSwitcherViewController: NSCollectionViewDataSource {
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return apps.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("AppSwitcherCell"), 
                                          for: indexPath) as! AppSwitcherCollectionViewItem
        
        let app = apps[indexPath.item]
        item.configure(with: app)
        
        return item
    }
}

// MARK: - NSCollectionViewDelegate

extension AppSwitcherViewController: NSCollectionViewDelegate {
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        if let indexPath = indexPaths.first {
            selectedIndex = indexPath.item
            onAppSelected?(apps[selectedIndex])
        }
    }
}

// MARK: - AppSwitcherCollectionViewItem

class AppSwitcherCollectionViewItem: NSCollectionViewItem {

    private var iconImageView: NSImageView!
    private var nameLabel: NSTextField!
    private var shortcutLabel: NSTextField!
    private var containerView: NSView!

    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 100, height: 90))
        setupUI()
        setupTrackingArea()
    }

    private func setupTrackingArea() {
        let trackingArea = NSTrackingArea(
            rect: view.bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow],
            owner: self,
            userInfo: nil
        )
        view.addTrackingArea(trackingArea)
    }

    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)
        if !isSelected {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.1
                containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.4).cgColor
            }
        }
    }

    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)
        if !isSelected {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.1
                containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.2).cgColor
            }
        }
    }

    private func setupUI() {
        // Container view with background
        containerView = NSView()
        containerView.wantsLayer = true
        containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.2).cgColor
        containerView.layer?.cornerRadius = 6
        containerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(containerView)

        // Icon (larger and more prominent)
        iconImageView = NSImageView()
        iconImageView.imageScaling = .scaleProportionallyUpOrDown
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconImageView)

        // App name (smaller, at bottom)
        nameLabel = NSTextField(labelWithString: "")
        nameLabel.font = NSFont.systemFont(ofSize: 10, weight: .medium)
        nameLabel.textColor = NSColor.labelColor
        nameLabel.alignment = .center
        nameLabel.isEditable = false
        nameLabel.isBordered = false
        nameLabel.backgroundColor = NSColor.clear
        nameLabel.maximumNumberOfLines = 1
        nameLabel.lineBreakMode = .byTruncatingTail
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)

        // Shortcut (very small, at bottom corner)
        shortcutLabel = NSTextField(labelWithString: "")
        shortcutLabel.font = NSFont.systemFont(ofSize: 8)
        shortcutLabel.textColor = NSColor.tertiaryLabelColor
        shortcutLabel.alignment = .center
        shortcutLabel.isEditable = false
        shortcutLabel.isBordered = false
        shortcutLabel.backgroundColor = NSColor.clear
        shortcutLabel.maximumNumberOfLines = 1
        shortcutLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(shortcutLabel)

        // Auto Layout constraints - more compact layout
        NSLayoutConstraint.activate([
            // Container view fills the entire cell
            containerView.topAnchor.constraint(equalTo: view.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // Icon takes most of the space, centered - adaptive size
            iconImageView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconImageView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 6),
            iconImageView.widthAnchor.constraint(lessThanOrEqualTo: containerView.widthAnchor, multiplier: 0.7),
            iconImageView.heightAnchor.constraint(lessThanOrEqualTo: containerView.heightAnchor, multiplier: 0.6),
            iconImageView.widthAnchor.constraint(greaterThanOrEqualToConstant: 32),
            iconImageView.heightAnchor.constraint(greaterThanOrEqualToConstant: 32),

            // App name at the bottom
            nameLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 4),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 2),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -2),
            nameLabel.heightAnchor.constraint(equalToConstant: 12),

            // Shortcut at the very bottom
            shortcutLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            shortcutLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 2),
            shortcutLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -2),
            shortcutLabel.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -4),
            shortcutLabel.heightAnchor.constraint(equalToConstant: 10)
        ])
    }

    func configure(with app: AppModel) {
        iconImageView.image = app.icon
        nameLabel.stringValue = app.appDisplayName

        // Format shortcut display - more compact
        if let shortcut = app.shortcut {
            shortcutLabel.stringValue = shortcut.description
        } else {
            shortcutLabel.stringValue = ""
        }
    }

    override var isSelected: Bool {
        didSet {
            updateSelection()
        }
    }

    private func updateSelection() {
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.15
            context.allowsImplicitAnimation = true

            if isSelected {
                containerView.layer?.backgroundColor = NSColor.selectedControlColor.withAlphaComponent(0.8).cgColor
                containerView.layer?.borderWidth = 2
                containerView.layer?.borderColor = NSColor.selectedControlColor.cgColor

                // Add subtle shadow and glow for selected item
                containerView.layer?.shadowColor = NSColor.selectedControlColor.cgColor
                containerView.layer?.shadowOffset = NSSize(width: 0, height: 2)
                containerView.layer?.shadowRadius = 6
                containerView.layer?.shadowOpacity = 0.4

                // Slightly scale up the selected item
                containerView.layer?.transform = CATransform3DMakeScale(1.05, 1.05, 1.0)
            } else {
                containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.2).cgColor
                containerView.layer?.borderWidth = 0
                containerView.layer?.shadowOpacity = 0
                containerView.layer?.transform = CATransform3DIdentity
            }
        }
    }
}

