//
//  AppDelegate.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 <PERSON><PERSON><PERSON>. All rights reserved.
//

import Cocoa
import KeyboardShortcuts
import LaunchAtLogin

@NSApplicationMain
class AppDelegate: NSObject, NSApplicationDelegate {

    // MARK: Properties

    var hasLaunched = false
    var isGoingToDisableShortcut = false

    var anewShortcutTimer: Timer?
    var delayTimer: Timer?

    var mainWindowController: MainWindowController?
    var appSwitcherWindow: NSWindow?

    @IBOutlet weak var statusItemController: StatusItemController!

    // MARK: Life cycle

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        defaults.register(defaults: [
            DefaultsKeys.DeactivateKey.key: 0,
            DefaultsKeys.DelayInterval.key: 0.3,
            DefaultsKeys.EnableShortcut.key: true,
            DefaultsKeys.enableMenuBarIcon.key: true,
            DefaultsKeys.enableMenuBarIconShowHideKey.key: true,
            DefaultsKeys.enableAppSwitcher.key: true
            ])

        NSApp.setActivationPolicy(.accessory)

        if defaults[.enableMenuBarIcon] {
            sharedAppDelegate?.statusItemController.showInMenuBar()
        } else {
            sharedAppDelegate?.statusItemController.hideInMenuBar()
        }

        if defaults[.enableMenuBarIconShowHideKey] {
            registerMenubarIconShortcut()
        }

        shortcutEnableMonitor()

        if defaults.object(forKey: DefaultsKeys.LaunchAtLoginKey.key) != nil {
            defaults.removeObject(forKey: DefaultsKeys.LaunchAtLoginKey.key)
            LaunchAtLogin.isEnabled = defaults[.LaunchAtLoginKey]
        }

        // KeyboardShortcuts doesn't need explicit validation setup
        ShortcutMonitor.register()

        // One-time setup for new users: enable App Switcher and set default shortcut
        setupAppSwitcherForFirstTime()

        // Register App Switcher shortcut if enabled
        if defaults[.enableAppSwitcher] {
            registerAppSwitcherShortcut()
        }

        if AppsManager.manager.selectedApps.count == 0 {
            showMainWindow()
        }
    }

    func applicationWillBecomeActive(_ notification: Notification) {
        if hasLaunched {
            showMainWindow()
        } else {
            hasLaunched = true
        }
    }

    func showMainWindow() {
        if let rootViewController = mainWindowController {
            rootViewController.showWindow(nil)
        } else {
            let storyboard = NSStoryboard(name: "Main", bundle: nil)
            let viewController = storyboard.instantiateController(withIdentifier: MainWindowController.className)
            let rootViewController = viewController as? MainWindowController
            mainWindowController = rootViewController
            mainWindowController?.showWindow(nil)
        }

        NSApp.activate(ignoringOtherApps: true)
    }

    // MARK: Listen events

    private func shortcutEnableMonitor() {
        let delayInterval: TimeInterval        = 0.3

        let shortcutActivateHandler = { (event: NSEvent) in
            let anewShortcutInterval: TimeInterval = defaults[.DelayInterval]
            let supportedModifiers = [
                NSEvent.ModifierFlags.shift,
                NSEvent.ModifierFlags.control,
                NSEvent.ModifierFlags.option,
                NSEvent.ModifierFlags.command
            ]
            let deactivateKey = supportedModifiers[defaults[.DeactivateKey]]
            let modifier = event.modifierFlags.intersection(deactivateKey)

            if modifier == deactivateKey && defaults[.EnableDeactivateKey] {
                if self.isGoingToDisableShortcut {
                    self.isGoingToDisableShortcut = false

                    ShortcutMonitor.unregister()
                    defaults[.EnableShortcut] = false

                    self.anewShortcutTimer = Timer(timeInterval: anewShortcutInterval,
                                                   target: self,
                                                   selector: #selector(self.anewShortcutEnable),
                                                   userInfo: nil,
                                                   repeats: false)
                    RunLoop.current.add(self.anewShortcutTimer!, forMode: RunLoop.Mode.common)
                } else {
                    self.isGoingToDisableShortcut = true

                    self.delayTimer = Timer(timeInterval: delayInterval,
                                            target: self,
                                            selector: #selector(self.checkShortcutEnable(_:)),
                                            userInfo: nil,
                                            repeats: false)
                    RunLoop.current.add(self.delayTimer!, forMode: RunLoop.Mode.common)
                }
            }
        }

        NSEvent.addGlobalMonitorForEvents(matching: [.flagsChanged], handler: { (event) in
            shortcutActivateHandler(event)
        })

        NSEvent.addLocalMonitorForEvents(matching: [.flagsChanged], handler: { (event) -> NSEvent? in
            shortcutActivateHandler(event)
            return event
        })
    }

    @objc private func checkShortcutEnable(_ timer: Timer) {
        delayTimer?.invalidate()
        delayTimer = nil

        isGoingToDisableShortcut = false
    }

    @objc private func anewShortcutEnable(_ timer: Timer) {
        anewShortcutTimer?.invalidate()
        anewShortcutTimer = nil

        defaults[.EnableShortcut] = true
        ShortcutMonitor.register()
    }

    func registerMenubarIconShortcut() {
        KeyboardShortcuts.onKeyUp(for: .toggleMenuBarIcon) {
            defaults[.enableMenuBarIcon] = !defaults[.enableMenuBarIcon]

            if defaults[.enableMenuBarIcon] {
                sharedAppDelegate?.statusItemController.showInMenuBar()
            } else {
                sharedAppDelegate?.statusItemController.hideInMenuBar()
            }

            NotificationCenter.default.post(name: .updateMenuBarToggleState, object: nil)
        }
    }

    func unregisterMenubarIconShortcut() {
        // KeyboardShortcuts automatically manages listeners
        // We could store the listener reference if we need to remove it specifically
    }

    func registerAppSwitcherShortcut() {
        // Toggle App Switcher on key up
        KeyboardShortcuts.onKeyUp(for: .showAppSwitcher) { [weak self] in
            guard defaults[.enableAppSwitcher] else { return }
            self?.toggleAppSwitcher()
        }
    }

    func unregisterAppSwitcherShortcut() {
        // KeyboardShortcuts automatically manages listeners
        // We could store the listener reference if we need to remove it specifically
    }

    func showAppSwitcher() {
        // Don't show if already visible
        if let window = appSwitcherWindow, window.isVisible {
            return
        }

        if appSwitcherWindow == nil {
            // Create the window programmatically
            let window = NSWindow(contentRect: NSRect(x: 0, y: 0, width: 600, height: 400),
                                styleMask: [.borderless],
                                backing: .buffered,
                                defer: false)

            // Configure window appearance
            window.backgroundColor = NSColor.clear
            window.isOpaque = false
            window.hasShadow = true
            window.level = NSWindow.Level.floating
            window.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
            window.isMovableByWindowBackground = true // Optional: allows dragging by background

            // Create and set the view controller
            let storyboard = NSStoryboard(name: "Main", bundle: nil)
            if let viewController = storyboard.instantiateController(withIdentifier: "AppSwitcherViewController") as? AppSwitcherViewController {
                window.contentViewController = viewController

                // Set up callbacks with weak references to prevent retain cycles
                viewController.onClose = { [weak self] in
                    self?.hideAppSwitcher()
                }

                viewController.onAppSelected = { [weak self] app in
                    guard let self = self else { return }
                    self.hideAppSwitcher()
                    self.launchApp(app)
                }
            }

            appSwitcherWindow = window
        }

        // Center and show window
        appSwitcherWindow?.center()
        appSwitcherWindow?.makeKeyAndOrderFront(nil) // This will now work correctly

        // Refresh the app list
        if let viewController = appSwitcherWindow?.contentViewController as? AppSwitcherViewController {
            viewController.refreshApps()
        }
    }

    func hideAppSwitcher() {
        guard let window = appSwitcherWindow, window.isVisible else { return }

        // Just hide the window, don't set to nil so it can be reused
        window.orderOut(nil)
    }

    func toggleAppSwitcher() {
        if let window = appSwitcherWindow, window.isVisible {
            hideAppSwitcher()
        } else {
            showAppSwitcher()
        }
    }

    private func setupAppSwitcherForFirstTime() {
        // Check if this is the first time the app is launched
        if !defaults[.appSwitcherFirstTimeSetup] {
            // First time setup: enable App Switcher by default
            defaults[.enableAppSwitcher] = true

            // Set the default shortcut (Option+Tab) if not already set
            if KeyboardShortcuts.Name.showAppSwitcher.shortcut == nil {
                KeyboardShortcuts.Name.showAppSwitcher.shortcut = KeyboardShortcuts.Shortcut(.tab, modifiers: [.option])
            }

            // Mark that we've done the first-time setup
            defaults[.appSwitcherFirstTimeSetup] = true

            NSLog("App Switcher enabled for first-time user with Option+Tab shortcut")
        }
    }

    // MARK: - Development Helper (can be removed in production)
    private func resetFirstTimeSetup() {
        // Uncomment this line to test first-time setup again
        defaults[.appSwitcherFirstTimeSetup] = false
    }

    private func launchApp(_ app: AppModel) {
        if let frontmostAppIdentifier = NSWorkspace.shared.frontmostApplication?.bundleIdentifier,
           let targetAppIdentifier = Bundle(url: app.appBundleURL)?.bundleIdentifier,
           frontmostAppIdentifier == targetAppIdentifier {
            // If the app is already frontmost, hide it
            NSRunningApplication.runningApplications(withBundleIdentifier: frontmostAppIdentifier).first?.hide()
        } else {
            // Launch or bring the app to front
            let configuration = NSWorkspace.OpenConfiguration()
            configuration.activates = true
            NSWorkspace.shared.openApplication(at: app.appBundleURL,
                                               configuration: configuration)
            { _, error in
                if let error = error {
                    NSLog("ERROR launching app: \(error)")
                }
            }
        }
    }

}
